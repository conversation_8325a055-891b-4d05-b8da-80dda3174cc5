import frappe


def execute():
    """Remove invalid student field from Sales Order that references non-existent Student doctype"""

    try:
        # Check if the problematic custom field exists
        student_field = frappe.db.get_value(
            "Custom Field",
            {"dt": "Sales Order", "fieldname": "student"},
            ["name", "options"],
        )

        if student_field:
            field_name = (
                student_field[0] if isinstance(student_field, tuple) else student_field
            )
            field_options = (
                student_field[1] if isinstance(student_field, tuple) else None
            )

            print(
                f"Found problematic student field: {field_name} with options: {field_options}"
            )

            # Check if Student doctype exists
            if not frappe.db.exists("DocType", "Student"):
                print(
                    "Student doctype does not exist. Removing invalid custom field..."
                )

                # Delete the custom field
                frappe.delete_doc("Custom Field", field_name, force=True)
                print(f"Successfully removed invalid student field: {field_name}")

                # Reload the Sales Order doctype
                frappe.reload_doctype("Sales Order")
                print("Sales Order doctype reloaded")

                # Clear cache
                frappe.clear_cache(doctype="Sales Order")
                print("Cache cleared for Sales Order")

            else:
                print("Student doctype exists. Field is valid.")
        else:
            print("No student field found in Sales Order")

    except Exception as e:
        print(f"Error removing student field from Sales Order: {str(e)}")
        frappe.log_error(f"Error removing student field from Sales Order: {str(e)}")
