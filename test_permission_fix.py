#!/usr/bin/env python3

import frappe
from erpnext.accounts.doctype.journal_entry.journal_entry import get_default_bank_cash_account

def test_permission_bypass():
    """Test that the permission bypass works for get_default_bank_cash_account"""
    
    # Set up test environment
    frappe.init(site="all")
    frappe.connect()
    
    try:
        # Get a company to test with
        companies = frappe.get_list("Company", limit=1)
        if not companies:
            print("No companies found in the system")
            return
        
        company = companies[0].name
        print(f"Testing with company: {company}")
        
        # Test without permission bypass (should potentially fail)
        print("\n1. Testing without permission bypass...")
        try:
            result = get_default_bank_cash_account(company, "Bank")
            print(f"Success without bypass: {result}")
        except Exception as e:
            print(f"Failed without bypass: {str(e)}")
        
        # Test with permission bypass
        print("\n2. Testing with permission bypass...")
        frappe.flags.ignore_account_permission = True
        try:
            result = get_default_bank_cash_account(company, "Bank")
            print(f"Success with bypass: {result}")
        except Exception as e:
            print(f"Failed with bypass: {str(e)}")
        finally:
            frappe.flags.ignore_account_permission = False
        
        print("\nTest completed successfully!")
        
    except Exception as e:
        print(f"Test failed with error: {str(e)}")
    finally:
        frappe.destroy()

if __name__ == "__main__":
    test_permission_bypass()
