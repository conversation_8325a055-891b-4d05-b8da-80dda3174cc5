2025-06-12 13:02:25,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` MODIFY `vehicle_value` decimal(21,9) not null default 0, MODIFY `uom` varchar(140)
2025-06-12 13:02:26,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `custom_employment_status` varchar(140)
2025-06-12 13:02:27,131 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `round_off_for_opening` varchar(140), ADD COLUMN `book_advance_payments_in_separate_party_account` int(1) not null default 0, ADD COLUMN `reconcile_on_advance_payment_date` int(1) not null default 0, ADD COLUMN `reconciliation_takes_effect_on` varchar(140) default 'Oldest Of Invoice Or Advance', ADD COLUMN `default_advance_received_account` varchar(140), ADD COLUMN `default_advance_paid_account` varchar(140), ADD COLUMN `default_operating_cost_account` varchar(140)
2025-06-12 13:02:27,152 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-06-12 13:02:27,373 WARNING database DDL Query made to DB:
ALTER TABLE `tabAuthorization Rule` MODIFY `value` decimal(21,9) not null default 0
2025-06-12 13:02:27,697 WARNING database DDL Query made to DB:
create table `tabPlant Floor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`floor_name` varchar(140) unique,
`company` varchar(140),
`warehouse` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:27,898 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation` ADD COLUMN `plant_floor` varchar(140), ADD COLUMN `warehouse` varchar(140), ADD COLUMN `status` varchar(140), ADD COLUMN `on_status_image` text, ADD COLUMN `off_status_image` text, ADD COLUMN `total_working_hours` decimal(21,9) not null default 0
2025-06-12 13:02:27,920 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation` MODIFY `hour_rate_consumable` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `hour_rate_rent` decimal(21,9) not null default 0, MODIFY `hour_rate_labour` decimal(21,9) not null default 0, MODIFY `hour_rate_electricity` decimal(21,9) not null default 0
2025-06-12 13:02:28,102 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Item` MODIFY `planned_qty` decimal(21,9) not null default 0
2025-06-12 13:02:28,275 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Operation` MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `cost_per_unit` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `base_cost_per_unit` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `time_in_mins` decimal(21,9) not null default 0
2025-06-12 13:02:28,418 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation Working Hour` ADD COLUMN `hours` decimal(21,9) not null default 0
2025-06-12 13:02:28,693 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` ADD COLUMN `bom_creator` varchar(140), ADD COLUMN `bom_creator_item` varchar(140)
2025-06-12 13:02:28,714 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0
2025-06-12 13:02:28,991 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card` ADD COLUMN `time_required` decimal(21,9) not null default 0, ADD COLUMN `actual_start_date` datetime(6), ADD COLUMN `actual_end_date` datetime(6), ADD COLUMN `serial_and_batch_bundle` varchar(140)
2025-06-12 13:02:29,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card` MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_time_in_mins` decimal(21,9) not null default 0, MODIFY `for_quantity` decimal(21,9) not null default 0
2025-06-12 13:02:29,197 WARNING database DDL Query made to DB:
ALTER TABLE `tabBlanket Order` ADD COLUMN `order_no` varchar(140), ADD COLUMN `order_date` date
2025-06-12 13:02:29,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Operation` MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `batch_size` decimal(21,9) not null default 0, MODIFY `time_in_mins` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `completed_qty` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operation_time` decimal(21,9) not null default 0
2025-06-12 13:02:29,520 WARNING database DDL Query made to DB:
create table `tabJob Card Scheduled Time` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_time` datetime(6),
`to_time` datetime(6),
`time_in_mins` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:29,719 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` ADD COLUMN `ordered_qty` decimal(21,9) not null default 0
2025-06-12 13:02:29,741 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `wo_produced_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0
2025-06-12 13:02:30,256 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0
2025-06-12 13:02:30,407 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Time Log` MODIFY `time_in_mins` decimal(21,9) not null default 0
2025-06-12 13:02:30,568 WARNING database DDL Query made to DB:
create table `tabBOM Creator` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`qty` decimal(21,9) not null default 0,
`project` varchar(140),
`uom` varchar(140),
`rm_cost_as_per` varchar(140) default 'Valuation Rate',
`set_rate_based_on_warehouse` int(1) not null default 0,
`buying_price_list` varchar(140),
`price_list_currency` varchar(140),
`plc_conversion_rate` decimal(21,9) not null default 0,
`currency` varchar(140),
`conversion_rate` decimal(21,9) not null default 1.0,
`default_warehouse` varchar(140),
`company` varchar(140),
`raw_material_cost` decimal(21,9) not null default 0,
`remarks` longtext,
`status` varchar(140) default 'Draft',
`error_log` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:30,733 WARNING database DDL Query made to DB:
create table `tabBOM Creator Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`fg_item` varchar(140),
`source_warehouse` varchar(140),
`is_expandable` int(1) not null default 0,
`sourced_by_supplier` int(1) not null default 0,
`bom_created` int(1) not null default 0,
`allow_alternative_item` int(1) not null default 1,
`description` text,
`qty` decimal(21,9) not null default 0,
`rate` decimal(21,9) not null default 0,
`uom` varchar(140),
`stock_qty` decimal(21,9) not null default 0,
`conversion_factor` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`amount` decimal(21,9) not null default 0,
`base_rate` decimal(21,9) not null default 0,
`base_amount` decimal(21,9) not null default 0,
`do_not_explode` int(1) not null default 1,
`parent_row_no` varchar(140),
`fg_reference_id` varchar(140),
`instruction` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:31,353 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Price` MODIFY `price_list_rate` decimal(21,9) not null default 0
2025-06-12 13:02:31,638 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140)
2025-06-12 13:02:31,660 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `basic_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `additional_cost` decimal(21,9) not null default 0, MODIFY `basic_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-12 13:02:31,692 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`)
2025-06-12 13:02:31,912 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment` ADD COLUMN `total_weight` decimal(21,9) not null default 0
2025-06-12 13:02:31,934 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment` MODIFY `pickup_from` time(6) default '09:00', MODIFY `pickup_to` time(6) default '17:00', MODIFY `value_of_goods` decimal(21,9) not null default 0, MODIFY `shipment_amount` decimal(21,9) not null default 0
2025-06-12 13:02:32,170 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` ADD COLUMN `ignore_pricing_rule` int(1) not null default 0
2025-06-12 13:02:32,191 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` MODIFY `for_qty` decimal(21,9) not null default 0
2025-06-12 13:02:32,597 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `company_total_stock` decimal(21,9) not null default 0
2025-06-12 13:02:32,620 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-12 13:02:32,654 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`)
2025-06-12 13:02:33,084 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `reconcile_all_serial_batch` int(1) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `current_serial_and_batch_bundle` varchar(140)
2025-06-12 13:02:33,107 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `current_valuation_rate` decimal(21,9) not null default 0, MODIFY `amount_difference` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `current_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 13:02:33,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`)
2025-06-12 13:02:33,559 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `asset_repair` varchar(140)
2025-06-12 13:02:33,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-06-12 13:02:33,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Default` ADD INDEX `default_warehouse_index`(`default_warehouse`)
2025-06-12 13:02:33,973 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Attribute` ADD COLUMN `disabled` int(1) not null default 0
2025-06-12 13:02:34,174 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD COLUMN `recreate_stock_ledgers` int(1) not null default 0, ADD COLUMN `reposting_data_file` text, ADD COLUMN `total_reposting_count` int(11) not null default 0
2025-06-12 13:02:34,205 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:34,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `witholding_tax_rate_on_purchase` decimal(21,9) not null default 0, MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate_on_sales` decimal(21,9) not null default 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0
2025-06-12 13:02:34,795 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Variant Attribute` ADD COLUMN `disabled` int(1) not null default 0
2025-06-12 13:02:34,815 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Variant Attribute` MODIFY `to_range` decimal(21,9) not null default 0, MODIFY `from_range` decimal(21,9) not null default 0, MODIFY `increment` decimal(21,9) not null default 0
2025-06-12 13:02:35,010 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` ADD COLUMN `auto_created_serial_and_batch_bundle` int(1) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `has_batch_no` int(1) not null default 0, ADD COLUMN `has_serial_no` int(1) not null default 0
2025-06-12 13:02:35,031 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` MODIFY `stock_value` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty_after_transaction` decimal(21,9) not null default 0, MODIFY `outgoing_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `stock_value_difference` decimal(21,9) not null default 0
2025-06-12 13:02:35,069 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` ADD INDEX `voucher_type_index`(`voucher_type`), ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`)
2025-06-12 13:02:35,101 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `item_code`, DROP INDEX `warehouse`
2025-06-12 13:02:35,174 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_posting_datetime_creation_index`(item_code, warehouse, posting_datetime, creation)
2025-06-12 13:02:35,334 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140)
2025-06-12 13:02:35,355 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0
2025-06-12 13:02:35,387 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` ADD INDEX `parent_detail_docname_index`(`parent_detail_docname`)
2025-06-12 13:02:35,726 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection` ADD COLUMN `company` varchar(140), ADD COLUMN `child_row_reference` varchar(140), ADD COLUMN `letter_head` varchar(140)
2025-06-12 13:02:35,747 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection` MODIFY `sample_size` decimal(21,9) not null default 0
2025-06-12 13:02:36,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0, ADD COLUMN `apply_tds` int(1) not null default 1, ADD COLUMN `sales_incoming_rate` decimal(21,9) not null default 0, ADD COLUMN `amount_difference_with_purchase_invoice` decimal(21,9) not null default 0, ADD COLUMN `return_qty_from_rejected_warehouse` int(1) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `rejected_serial_and_batch_bundle` varchar(140), ADD COLUMN `subcontracting_receipt_item` varchar(140)
2025-06-12 13:02:36,127 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `received_stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-12 13:02:36,172 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`), ADD INDEX `batch_no_index`(`batch_no`), ADD INDEX `subcontracting_receipt_item_index`(`subcontracting_receipt_item`)
2025-06-12 13:02:36,590 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-06-12 13:02:36,783 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Type` ADD COLUMN `is_standard` int(1) not null default 0
2025-06-12 13:02:36,941 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Reorder` MODIFY `warehouse_reorder_qty` decimal(21,9) not null default 0, MODIFY `warehouse_reorder_level` decimal(21,9) not null default 0
2025-06-12 13:02:37,068 WARNING database DDL Query made to DB:
create table `tabSerial and Batch Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`serial_no` varchar(140),
`batch_no` varchar(140),
`qty` decimal(21,9) not null default 1.0,
`warehouse` varchar(140),
`delivered_qty` decimal(21,9) not null default 0,
`incoming_rate` decimal(21,9) not null default 0,
`outgoing_rate` decimal(21,9) not null default 0,
`stock_value_difference` decimal(21,9) not null default 0,
`is_outward` int(1) not null default 0,
`stock_queue` text,
index `serial_no`(`serial_no`),
index `batch_no`(`batch_no`),
index `warehouse`(`warehouse`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:37,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabPutaway Rule` MODIFY `stock_capacity` decimal(21,9) not null default 0
2025-06-12 13:02:37,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` MODIFY `grand_total` decimal(21,9) not null default 0
2025-06-12 13:02:37,849 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial No` MODIFY `purchase_rate` decimal(21,9) not null default 0
2025-06-12 13:02:37,876 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial No` DROP INDEX `warehouse`
2025-06-12 13:02:38,297 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` ADD COLUMN `subcontracting_receipt` varchar(140), ADD COLUMN `tax_withholding_net_total` decimal(21,9) not null default 0, ADD COLUMN `base_tax_withholding_net_total` decimal(21,9) not null default 0, ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-06-12 13:02:38,319 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0
2025-06-12 13:02:38,353 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` ADD INDEX `subcontracting_receipt_index`(`subcontracting_receipt`)
2025-06-12 13:02:38,553 WARNING database DDL Query made to DB:
create table `tabSerial and Batch Bundle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140) default 'SABB-.########',
`company` varchar(140),
`item_name` varchar(140),
`has_serial_no` int(1) not null default 0,
`has_batch_no` int(1) not null default 0,
`item_code` varchar(140),
`warehouse` varchar(140),
`type_of_transaction` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`item_group` varchar(140),
`avg_rate` decimal(21,9) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`voucher_detail_no` varchar(140),
`posting_date` date,
`posting_time` time(6),
`returned_against` varchar(140),
`is_cancelled` int(1) not null default 0,
`is_rejected` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `item_code`(`item_code`),
index `warehouse`(`warehouse`),
index `type_of_transaction`(`type_of_transaction`),
index `voucher_no`(`voucher_no`),
index `voucher_detail_no`(`voucher_detail_no`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:38,809 WARNING database DDL Query made to DB:
ALTER TABLE `tabBin` ADD COLUMN `reserved_stock` decimal(21,9) not null default 0
2025-06-12 13:02:38,831 WARNING database DDL Query made to DB:
ALTER TABLE `tabBin` MODIFY `reserved_qty_for_production_plan` decimal(21,9) not null default 0, MODIFY `stock_value` decimal(21,9) not null default 0, MODIFY `planned_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `reserved_qty_for_production` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `reserved_qty_for_sub_contract` decimal(21,9) not null default 0
2025-06-12 13:02:39,069 WARNING database DDL Query made to DB:
create table `tabStock Reservation Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`warehouse` varchar(140),
`has_serial_no` int(1) not null default 0,
`has_batch_no` int(1) not null default 0,
`voucher_type` varchar(140),
`voucher_no` varchar(140),
`voucher_detail_no` varchar(140),
`from_voucher_type` varchar(140),
`from_voucher_no` varchar(140),
`from_voucher_detail_no` varchar(140),
`stock_uom` varchar(140),
`available_qty` decimal(21,9) not null default 0,
`voucher_qty` decimal(21,9) not null default 0,
`reserved_qty` decimal(21,9) not null default 0,
`delivered_qty` decimal(21,9) not null default 0,
`reservation_based_on` varchar(140) default 'Qty',
`company` varchar(140),
`project` varchar(140),
`status` varchar(140) default 'Draft',
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `item_code`(`item_code`),
index `warehouse`(`warehouse`),
index `voucher_no`(`voucher_no`),
index `voucher_detail_no`(`voucher_detail_no`),
index `from_voucher_no`(`from_voucher_no`),
index `company`(`company`),
index `project`(`project`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:39,380 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Taxes and Charges` ADD COLUMN `has_corrective_cost` int(1) not null default 0
2025-06-12 13:02:39,402 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Taxes and Charges` MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 13:02:39,622 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `min_order_qty` decimal(21,9) not null default 0
2025-06-12 13:02:39,991 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` ADD COLUMN `stock_reserved_qty` decimal(21,9) not null default 0, ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0
2025-06-12 13:02:40,012 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-12 13:02:40,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` ADD INDEX `serial_and_batch_bundle_index`(`serial_and_batch_bundle`)
2025-06-12 13:02:40,068 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` DROP INDEX `sales_order_item_index`
2025-06-12 13:02:40,307 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_ordered` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-06-12 13:02:40,548 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Trip` MODIFY `total_distance` decimal(21,9) not null default 0
2025-06-12 13:02:41,430 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` ADD COLUMN `sla_resolution_by` datetime(6), ADD COLUMN `sla_resolution_date` datetime(6)
2025-06-12 13:02:41,454 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssue` MODIFY `avg_response_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9)
2025-06-12 13:02:42,103 WARNING database DDL Query made to DB:
create table `tabPortal User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
index `user`(`user`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:42,353 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Shift Allocation` ADD INDEX `amended_from_index`(`amended_from`)
2025-06-12 13:02:42,613 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair` MODIFY `total_repair_cost` decimal(21,9) not null default 0
2025-06-12 13:02:42,871 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization` MODIFY `stock_items_total` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `service_items_total` decimal(21,9) not null default 0, MODIFY `asset_items_total` decimal(21,9) not null default 0, MODIFY `target_incoming_rate` decimal(21,9) not null default 0
2025-06-12 13:02:43,035 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Category` ADD COLUMN `non_depreciable_category` int(1) not null default 0
2025-06-12 13:02:43,214 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Value Adjustment` ADD COLUMN `difference_account` varchar(140)
2025-06-12 13:02:43,235 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Value Adjustment` MODIFY `current_asset_value` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `new_asset_value` decimal(21,9) not null default 0
2025-06-12 13:02:43,434 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` ADD COLUMN `purchase_receipt_item` varchar(140), ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0
2025-06-12 13:02:43,456 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0
2025-06-12 13:02:43,488 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:43,794 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` ADD COLUMN `purchase_receipt_item` varchar(140), ADD COLUMN `purchase_invoice_item` varchar(140), ADD COLUMN `opening_number_of_booked_depreciations` int(11) not null default 0, ADD COLUMN `purchase_amount` decimal(21,9) not null default 0
2025-06-12 13:02:43,815 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0, MODIFY `total_asset_cost` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0, MODIFY `additional_asset_cost` decimal(21,9) not null default 0
2025-06-12 13:02:43,960 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair Consumed Item` ADD COLUMN `warehouse` varchar(140), ADD COLUMN `serial_and_batch_bundle` varchar(140)
2025-06-12 13:02:43,982 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair Consumed Item` MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0
2025-06-12 13:02:44,099 WARNING database DDL Query made to DB:
create table `tabAsset Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`date` datetime(6),
`user` varchar(140),
`subject` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:44,294 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Finance Book` ADD COLUMN `total_number_of_booked_depreciations` int(11) not null default 0
2025-06-12 13:02:44,317 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Finance Book` MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `rate_of_depreciation` decimal(21,9) not null default 0, MODIFY `salvage_value_percentage` decimal(21,9) not null default 0
2025-06-12 13:02:44,459 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepreciation Schedule` MODIFY `depreciation_amount` decimal(21,9) not null default 0, MODIFY `accumulated_depreciation_amount` decimal(21,9) not null default 0
2025-06-12 13:02:44,656 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Maintenance Log` ADD COLUMN `task_assignee_email` varchar(140)
2025-06-12 13:02:44,815 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Service Item` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 13:02:44,960 WARNING database DDL Query made to DB:
create table `tabAsset Depreciation Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset` varchar(140),
`naming_series` varchar(140),
`company` varchar(140),
`gross_purchase_amount` decimal(21,9) not null default 0,
`opening_accumulated_depreciation` decimal(21,9) not null default 0,
`opening_number_of_booked_depreciations` int(11) not null default 0,
`finance_book` varchar(140),
`finance_book_id` int(11) not null default 0,
`depreciation_method` varchar(140),
`total_number_of_depreciations` int(11) not null default 0,
`rate_of_depreciation` decimal(21,9) not null default 0,
`daily_prorata_based` int(1) not null default 0,
`shift_based` int(1) not null default 0,
`frequency_of_depreciation` int(11) not null default 0,
`expected_value_after_useful_life` decimal(21,9) not null default 0,
`notes` text,
`status` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:45,118 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Shift Factor` MODIFY `shift_factor` decimal(21,9) not null default 0
2025-06-12 13:02:45,602 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaintenance Schedule Item` ADD COLUMN `serial_and_batch_bundle` varchar(140)
2025-06-12 13:02:47,347 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` ADD COLUMN `purchase_order_item` varchar(140), ADD COLUMN `subcontracting_conversion_factor` decimal(21,9) not null default 0, ADD COLUMN `job_card` varchar(140)
2025-06-12 13:02:47,369 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `rm_cost_per_qty` decimal(21,9) not null default 0, MODIFY `service_cost_per_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 13:02:47,402 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` ADD INDEX `purchase_order_item_index`(`purchase_order_item`)
2025-06-12 13:02:47,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Service Item` ADD COLUMN `purchase_order_item` varchar(140)
2025-06-12 13:02:47,578 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Service Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 13:02:47,610 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Service Item` ADD INDEX `purchase_order_item_index`(`purchase_order_item`)
2025-06-12 13:02:47,783 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `expense_account` varchar(140), ADD COLUMN `cost_center` varchar(140)
2025-06-12 13:02:47,805 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` MODIFY `required_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `current_stock` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 13:02:48,045 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Item` ADD COLUMN `is_scrap_item` int(1) not null default 0, ADD COLUMN `scrap_cost_per_qty` decimal(21,9) not null default 0, ADD COLUMN `reference_name` varchar(140), ADD COLUMN `serial_and_batch_bundle` varchar(140), ADD COLUMN `use_serial_batch_fields` int(1) not null default 0, ADD COLUMN `rejected_serial_and_batch_bundle` varchar(140), ADD COLUMN `purchase_order` varchar(140), ADD COLUMN `purchase_order_item` varchar(140)
2025-06-12 13:02:48,066 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0
2025-06-12 13:02:48,104 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Item` ADD INDEX `purchase_order_index`(`purchase_order`), ADD INDEX `purchase_order_item_index`(`purchase_order_item`)
2025-06-12 13:02:48,388 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt` MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0
2025-06-12 13:02:48,566 WARNING database DDL Query made to DB:
create table `tabSubcontracting BOM` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_active` int(1) not null default 1,
`finished_good` varchar(140),
`finished_good_qty` decimal(21,9) not null default 1.0,
`finished_good_uom` varchar(140),
`finished_good_bom` varchar(140),
`service_item` varchar(140),
`service_item_qty` decimal(21,9) not null default 1.0,
`service_item_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `finished_good`(`finished_good`),
index `finished_good_bom`(`finished_good_bom`),
index `service_item`(`service_item`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:48,824 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order` MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-06-12 13:02:49,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer` ADD COLUMN `inter_company_material_request` varchar(140)
2025-06-12 13:02:49,603 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile Attachment` MODIFY `file_attachment` text
2025-06-12 13:02:49,762 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`type` varchar(140) default 'Sales',
`verification_code` varchar(140) unique,
`verification_status` varchar(140) default 'Pending',
`verification_date` date,
`company_name` varchar(140),
`receipt_number` varchar(140),
`subtotal` decimal(21,9) not null default 0,
`total_tax` decimal(21,9) not null default 0,
`grand_total` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:49,964 WARNING database DDL Query made to DB:
create table `tabInter Company Material Request Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_name` varchar(140),
`item_code` varchar(140),
`uom` varchar(140),
`qty` decimal(21,9) not null default 0,
`batch_no` varchar(140),
`bom_no` varchar(140),
`transfer_qty` decimal(21,9) not null default 0,
`s_warehouse` varchar(140),
`t_warehouse` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:50,150 WARNING database DDL Query made to DB:
create table `tabInter Company Material Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`from_company` varchar(140),
`default_from_warehouse` varchar(140),
`to_company` varchar(140),
`default_to_warehouse` varchar(140),
`inter_company_stock_transfer` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:50,274 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`quantity` varchar(140),
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:51,313 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-12 13:02:51,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 13:02:51,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 13:02:52,458 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` MODIFY `time_to_fill` decimal(21,9), MODIFY `expected_compensation` decimal(21,9) not null default 0
2025-06-12 13:02:52,489 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:52,659 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` MODIFY `task_weight` decimal(21,9) not null default 0
2025-06-12 13:02:52,698 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:52,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:53,077 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Cycle` ADD COLUMN `calculate_final_score_based_on_formula` int(1) not null default 0, ADD COLUMN `final_score_formula` longtext
2025-06-12 13:02:53,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Cycle` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:53,349 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:53,525 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpected Skill Set` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:53,692 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter content` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:53,876 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Date` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:54,024 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term Template` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:54,198 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:54,359 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation Template` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:54,537 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Request` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:54,717 WARNING database DDL Query made to DB:
create sequence if not exists pwa_notification_id_seq nocache nocycle
2025-06-12 13:02:54,744 WARNING database DDL Query made to DB:
create table `tabPWA Notification` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`to_user` varchar(140),
`from_user` varchar(140),
`message` longtext,
`read` int(1) not null default 0,
`reference_document_type` varchar(140),
`reference_document_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `to_user`(`to_user`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:54,981 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` MODIFY `average_rating` decimal(3,2)
2025-06-12 13:02:55,021 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:55,246 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisee` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:55,463 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` ADD COLUMN `latitude` decimal(21,9) not null default 0, ADD COLUMN `longitude` decimal(21,9) not null default 0, ADD COLUMN `geolocation` longtext, ADD COLUMN `offshift` int(1) not null default 0
2025-06-12 13:02:55,502 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:55,741 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-12 13:02:55,773 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:55,969 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD COLUMN `actual_encashable_days` decimal(21,9) not null default 0, ADD COLUMN `encashment_days` decimal(21,9) not null default 0, ADD COLUMN `pay_via_payment_entry` int(1) not null default 0, ADD COLUMN `expense_account` varchar(140), ADD COLUMN `payable_account` varchar(140), ADD COLUMN `posting_date` date, ADD COLUMN `paid_amount` decimal(21,9) not null default 0, ADD COLUMN `cost_center` varchar(140), ADD COLUMN `status` varchar(140)
2025-06-12 13:02:55,990 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `leave_balance` decimal(21,9) not null default 0, MODIFY `encashment_amount` decimal(21,9) not null default 0
2025-06-12 13:02:56,021 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:56,195 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter Template` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:56,414 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0
2025-06-12 13:02:56,446 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:56,621 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Assignment` ADD COLUMN `shift_location` varchar(140), ADD COLUMN `shift_schedule_assignment` varchar(140)
2025-06-12 13:02:56,671 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Assignment` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:56,813 WARNING database DDL Query made to DB:
create table `tabShift Schedule Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`shift_schedule` varchar(140),
`shift_location` varchar(140),
`shift_status` varchar(140) default 'Active',
`enabled` int(1) not null default 1,
`create_shifts_after` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:02:56,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Allow` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:57,133 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` ADD COLUMN `actual_cost` decimal(21,9) not null default 0, ADD COLUMN `cost` decimal(21,9) not null default 0, ADD COLUMN `account` varchar(140), ADD COLUMN `action` varchar(140) default 'Return'
2025-06-12 13:02:57,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:57,464 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding Template` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:57,671 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Referral` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:57,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Period` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:58,052 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service Item` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:58,204 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `sanctioned_amount` decimal(21,9) not null default 0
2025-06-12 13:02:58,236 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:58,401 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Itinerary` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:58,576 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Feedback` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:58,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Training` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:58,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` MODIFY `goal_score` decimal(21,9) not null default 0, MODIFY `goal_completion` decimal(21,9) not null default 0, MODIFY `per_weightage` decimal(21,9) not null default 0
2025-06-12 13:02:58,913 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:59,045 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 13:02:59,079 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:59,258 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal` MODIFY `progress` decimal(21,9) not null default 0
2025-06-12 13:02:59,290 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:59,483 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview` MODIFY `expected_average_rating` decimal(3,2), MODIFY `average_rating` decimal(3,2)
2025-06-12 13:02:59,515 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:59,691 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary` ADD INDEX `creation`(`creation`)
2025-06-12 13:02:59,828 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` MODIFY `annual_allocation` decimal(21,9) not null default 0
2025-06-12 13:02:59,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:00,022 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` MODIFY `rating` decimal(3,2)
2025-06-12 13:03:00,053 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:00,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant Source` ADD UNIQUE INDEX IF NOT EXISTS source_name (`source_name`)
2025-06-12 13:03:00,240 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant Source` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:00,400 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterest` ADD UNIQUE INDEX IF NOT EXISTS interest (`interest`)
2025-06-12 13:03:00,429 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterest` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:00,597 WARNING database DDL Query made to DB:
ALTER TABLE `tabKRA` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:00,762 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Approver` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:00,899 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Criteria` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:01,090 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:01,311 WARNING database DDL Query made to DB:
ALTER TABLE `tabExit Interview` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:01,477 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:01,678 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` ADD COLUMN `posted_on` datetime(6), ADD COLUMN `closes_on` date, ADD COLUMN `closed_on` date, ADD COLUMN `employment_type` varchar(140), ADD COLUMN `location` varchar(140), ADD COLUMN `publish_applications_received` int(1) not null default 1, ADD COLUMN `salary_per` varchar(140) default 'Month'
2025-06-12 13:03:01,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` MODIFY `upper_range` decimal(21,9) not null default 0, MODIFY `lower_range` decimal(21,9) not null default 0
2025-06-12 13:03:01,731 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:01,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` MODIFY `leave_balance` decimal(21,9) not null default 0, MODIFY `total_leave_days` decimal(21,9) not null default 0
2025-06-12 13:03:02,021 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:02,108 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application`
				ADD INDEX IF NOT EXISTS `employee_from_date_to_date_index`(employee, from_date, to_date)
2025-06-12 13:03:02,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` MODIFY `expected_average_rating` decimal(3,2)
2025-06-12 13:03:02,247 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:02,416 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD UNIQUE INDEX IF NOT EXISTS offer_term (`offer_term`)
2025-06-12 13:03:02,446 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:02,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` MODIFY `total_claimed_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_sanctioned_amount` decimal(21,9) not null default 0, MODIFY `total_advance_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_amount_reimbursed` decimal(21,9) not null default 0
2025-06-12 13:03:02,716 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:02,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` MODIFY `total_leaves_encashed` decimal(21,9) not null default 0, MODIFY `total_leaves_allocated` decimal(21,9) not null default 0, MODIFY `new_leaves_allocated` decimal(21,9) not null default 0, MODIFY `unused_leaves` decimal(21,9) not null default 0, MODIFY `carry_forwarded_leaves_count` decimal(21,9) not null default 0
2025-06-12 13:03:03,011 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:03,207 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:03,360 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD UNIQUE INDEX IF NOT EXISTS identification_document_type (`identification_document_type`)
2025-06-12 13:03:03,396 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:03,571 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance Request` ADD COLUMN `shift` varchar(140)
2025-06-12 13:03:03,610 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance Request` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:03,779 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` MODIFY `per_weightage` decimal(21,9) not null default 0, MODIFY `score` decimal(21,9) not null default 0, MODIFY `score_earned` decimal(21,9) not null default 0
2025-06-12 13:03:03,811 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:03,942 WARNING database DDL Query made to DB:
create table `tabShift Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`shift_type` varchar(140),
`frequency` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:03:04,378 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grievance` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:04,555 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Type` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:04,730 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` MODIFY `total` decimal(21,9) not null default 0, MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 13:03:04,762 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:04,901 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` MODIFY `proficiency` decimal(3,2)
2025-06-12 13:03:04,933 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:05,076 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployment Type` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:05,281 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:05,442 WARNING database DDL Query made to DB:
ALTER TABLE `tabGrievance Type` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:05,589 WARNING database DDL Query made to DB:
create table `tabShift Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140) unique,
`checkin_radius` int(11) not null default 0,
`latitude` decimal(21,9) not null default 0,
`longitude` decimal(21,9) not null default 0,
`geolocation` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:03:05,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant` MODIFY `applicant_rating` decimal(3,2), MODIFY `lower_range` decimal(21,9) not null default 0, MODIFY `upper_range` decimal(21,9) not null default 0
2025-06-12 13:03:05,843 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant` ADD INDEX `job_title_index`(`job_title`), ADD INDEX `creation`(`creation`)
2025-06-12 13:03:05,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:06,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD COLUMN `half_day_status` varchar(140), ADD COLUMN `modify_half_day_status` int(1) not null default 0
2025-06-12 13:03:06,220 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0
2025-06-12 13:03:06,472 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:06,624 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Property History` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:06,756 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group User` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:06,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` ADD COLUMN `color` varchar(140) default 'Blue', ADD COLUMN `auto_update_last_sync` int(1) not null default 0, ADD COLUMN `enable_late_entry_marking` int(1) not null default 0, ADD COLUMN `enable_early_exit_marking` int(1) not null default 0
2025-06-12 13:03:06,959 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` MODIFY `working_hours_threshold_for_absent` decimal(21,9) not null default 0, MODIFY `working_hours_threshold_for_half_day` decimal(21,9) not null default 0
2025-06-12 13:03:06,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:07,157 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event Employee` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:07,310 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry` MODIFY `leaves` decimal(21,9) not null default 0
2025-06-12 13:03:07,413 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:07,578 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:07,765 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill Map` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:07,924 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grade` MODIFY `default_base_pay` decimal(21,9) not null default 0
2025-06-12 13:03:07,959 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grade` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:08,153 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Assignment` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:08,472 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:08,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` MODIFY `hours` decimal(21,9) not null default 0
2025-06-12 13:03:08,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:08,794 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterviewer` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:08,994 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompensatory Leave Request` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:09,772 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` MODIFY `total_estimated_cost` decimal(21,9) not null default 0, MODIFY `estimated_cost_per_position` decimal(21,9) not null default 0
2025-06-12 13:03:10,584 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:11,056 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Type` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:11,194 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurpose of Travel` ADD UNIQUE INDEX IF NOT EXISTS purpose_of_travel (`purpose_of_travel`)
2025-06-12 13:03:11,227 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurpose of Travel` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:11,366 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Account` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:11,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Promotion` MODIFY `revised_ctc` decimal(21,9) not null default 0, MODIFY `current_ctc` decimal(21,9) not null default 0
2025-06-12 13:03:11,558 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Promotion` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:11,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal` MODIFY `total_score` decimal(21,9) not null default 0, MODIFY `goal_score_percentage` decimal(21,9) not null default 0, MODIFY `self_score` decimal(21,9) not null default 0, MODIFY `final_score` decimal(21,9) not null default 0, MODIFY `avg_feedback_score` decimal(21,9) not null default 0
2025-06-12 13:03:11,819 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:12,029 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` ADD COLUMN `max_encashable_leaves` int(11) not null default 0, ADD COLUMN `non_encashable_leaves` int(11) not null default 0
2025-06-12 13:03:12,050 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` MODIFY `max_leaves_allowed` decimal(21,9) not null default 0, MODIFY `fraction_of_daily_salary_per_leave` decimal(21,9) not null default 0, MODIFY `maximum_carry_forwarded_leaves` decimal(21,9) not null default 0
2025-06-12 13:03:12,086 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:12,244 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` ADD COLUMN `return_amount` decimal(21,9) not null default 0
2025-06-12 13:03:12,264 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `unclaimed_amount` decimal(21,9) not null default 0
2025-06-12 13:03:12,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:12,596 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Transfer` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:12,798 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:12,953 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:13,110 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation Skill` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:13,280 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` ADD COLUMN `total_asset_recovery_cost` decimal(21,9) not null default 0
2025-06-12 13:03:13,301 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` MODIFY `total_payable_amount` decimal(21,9) not null default 0, MODIFY `total_receivable_amount` decimal(21,9) not null default 0
2025-06-12 13:03:13,332 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:13,479 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-12 13:03:13,509 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:13,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Performance Feedback` MODIFY `total_score` decimal(21,9) not null default 0
2025-06-12 13:03:13,705 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Performance Feedback` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:13,921 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` MODIFY `total_travel_cost` decimal(21,9) not null default 0
2025-06-12 13:03:13,953 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:14,233 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group` MODIFY `message` longtext default '<p>Please share what did you do today. If you reply by midnight, your response will be recorded!</p>'
2025-06-12 13:03:14,265 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:14,412 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Health Insurance` ADD UNIQUE INDEX IF NOT EXISTS health_insurance_name (`health_insurance_name`)
2025-06-12 13:03:14,444 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Health Insurance` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:14,588 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Detail` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:14,723 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` MODIFY `rating` decimal(3,2), MODIFY `per_weightage` decimal(21,9) not null default 0
2025-06-12 13:03:14,755 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:14,923 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD UNIQUE INDEX IF NOT EXISTS training_program (`training_program`)
2025-06-12 13:03:14,953 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:15,087 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` MODIFY `per_weightage` decimal(21,9) not null default 0
2025-06-12 13:03:15,118 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:16,038 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` MODIFY `max_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 13:03:16,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:16,173 WARNING database DDL Query made to DB:
create table `tabSalary Withholding Cycle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_date` date,
`to_date` date,
`is_salary_released` int(1) not null default 0,
`journal_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:03:16,427 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-12 13:03:16,460 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:16,653 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:16,816 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 13:03:16,865 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` ADD INDEX `employee_index`(`employee`), ADD INDEX `company_index`(`company`), ADD INDEX `payroll_period_index`(`payroll_period`), ADD INDEX `creation`(`creation`)
2025-06-12 13:03:17,038 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` MODIFY `exemption_amount` decimal(21,9) not null default 0, MODIFY `total_actual_amount` decimal(21,9) not null default 0
2025-06-12 13:03:17,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:17,237 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Period Date` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:17,361 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-06-12 13:03:17,393 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:17,677 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` ADD COLUMN `attach_proof` text
2025-06-12 13:03:17,698 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-12 13:03:17,728 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:17,909 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` MODIFY `variable` decimal(21,9) not null default 0, MODIFY `tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `subsistence_allowance` decimal(21,9) not null default 0, MODIFY `taxable_earnings_till_date` decimal(21,9) not null default 0, MODIFY `base` decimal(21,9) not null default 0
2025-06-12 13:03:17,962 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:18,139 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` MODIFY `bonus_amount` decimal(21,9) not null default 0
2025-06-12 13:03:18,171 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:18,492 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD COLUMN `salary_withholding` varchar(140), ADD COLUMN `salary_withholding_cycle` varchar(140)
2025-06-12 13:03:18,513 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0
2025-06-12 13:03:18,813 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD INDEX `start_date_index`(`start_date`), ADD INDEX `end_date_index`(`end_date`), ADD INDEX `payroll_entry_index`(`payroll_entry`), ADD INDEX `creation`(`creation`)
2025-06-12 13:03:18,924 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip`
				ADD INDEX IF NOT EXISTS `employee_start_date_end_date_index`(employee, start_date, end_date)
2025-06-12 13:03:19,048 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `max_amount_eligible` decimal(21,9) not null default 0
2025-06-12 13:03:19,080 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:19,261 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` MODIFY `incentive_amount` decimal(21,9) not null default 0
2025-06-12 13:03:19,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:19,453 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Cost Center` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:19,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component Account` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:19,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Employee Detail` ADD COLUMN `is_salary_withheld` int(1) not null default 0
2025-06-12 13:03:19,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Employee Detail` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:20,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `auto_repeat_end_date` date, MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-12 13:03:20,348 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:20,598 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD COLUMN `grade` varchar(140)
2025-06-12 13:03:20,620 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-06-12 13:03:20,703 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:20,885 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` MODIFY `total_declared_amount` decimal(21,9) not null default 0, MODIFY `total_exemption_amount` decimal(21,9) not null default 0
2025-06-12 13:03:20,917 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:21,082 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` MODIFY `to_amount` decimal(21,9) not null default 0
2025-06-12 13:03:21,113 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:21,246 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` MODIFY `working_hours` decimal(21,9) not null default 0
2025-06-12 13:03:21,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:21,425 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Loan` ADD COLUMN `loan_product` varchar(140)
2025-06-12 13:03:21,447 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Loan` MODIFY `total_payment` decimal(21,9) not null default 0, MODIFY `interest_amount` decimal(21,9) not null default 0, MODIFY `principal_amount` decimal(21,9) not null default 0
2025-06-12 13:03:21,579 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` MODIFY `percent` decimal(21,9) not null default 0, MODIFY `min_taxable_income` decimal(21,9) not null default 0, MODIFY `max_taxable_income` decimal(21,9) not null default 0
2025-06-12 13:03:21,611 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:21,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `default_amount` decimal(21,9) not null default 0, MODIFY `tax_on_flexible_benefit` decimal(21,9) not null default 0, MODIFY `tax_on_additional_salary` decimal(21,9) not null default 0, MODIFY `additional_amount` decimal(21,9) not null default 0
2025-06-12 13:03:23,078 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` ADD INDEX `salary_component_index`(`salary_component`), ADD INDEX `exempted_from_income_tax_index`(`exempted_from_income_tax`), ADD INDEX `is_tax_applicable_index`(`is_tax_applicable`), ADD INDEX `variable_based_on_taxable_salary_index`(`variable_based_on_taxable_salary`), ADD INDEX `creation`(`creation`)
2025-06-12 13:03:23,236 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:23,394 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Applicable Component` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:23,538 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` MODIFY `total_allocated_leaves` decimal(21,9) not null default 0, MODIFY `available_leaves` decimal(21,9) not null default 0, MODIFY `pending_leaves` decimal(21,9) not null default 0, MODIFY `used_leaves` decimal(21,9) not null default 0, MODIFY `expired_leaves` decimal(21,9) not null default 0
2025-06-12 13:03:23,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:23,732 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` MODIFY `remaining_benefit` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `max_benefits` decimal(21,9) not null default 0, MODIFY `pro_rata_dispensed_amount` decimal(21,9) not null default 0
2025-06-12 13:03:23,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:23,987 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0
2025-06-12 13:03:24,022 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:24,184 WARNING database DDL Query made to DB:
create table `tabSalary Withholding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`posting_date` date,
`payroll_frequency` varchar(140),
`number_of_withholding_cycles` int(11) not null default 0,
`status` varchar(140) default 'Draft',
`from_date` date,
`to_date` date,
`date_of_joining` date,
`relieving_date` date,
`reason_for_withholding_salary` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 13:03:24,340 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Sub Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-12 13:03:24,372 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Sub Category` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:24,516 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` MODIFY `fraction_of_applicable_earnings` decimal(21,9) not null default 0
2025-06-12 13:03:24,547 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` ADD INDEX `creation`(`creation`)
2025-06-12 13:03:24,732 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure` MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `total_earning` decimal(21,9) not null default 0, MODIFY `max_benefits` decimal(21,9) not null default 0, MODIFY `leave_encashment_amount_per_day` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0
2025-06-12 13:03:24,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure` ADD INDEX `company_index`(`company`), ADD INDEX `currency_index`(`currency`), ADD INDEX `payroll_frequency_index`(`payroll_frequency`), ADD INDEX `creation`(`creation`)
2025-06-12 13:03:25,442 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Loan` DROP INDEX `modified`
2025-06-12 13:03:25,496 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` DROP INDEX `modified`
2025-06-12 13:03:25,539 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Item` DROP INDEX `modified`
2025-06-12 13:03:25,575 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepreciation Schedule` DROP INDEX `modified`
2025-06-12 13:03:25,606 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` DROP INDEX `modified`
2025-06-12 13:03:25,645 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Barcode` DROP INDEX `modified`
2025-06-12 13:03:25,680 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Time Log` DROP INDEX `modified`
2025-06-12 13:03:25,713 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Operation` DROP INDEX `modified`
2025-06-12 13:03:25,749 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject User` DROP INDEX `modified`
2025-06-12 13:03:25,783 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation Item` DROP INDEX `modified`
2025-06-12 13:03:25,817 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` DROP INDEX `modified`
2025-06-12 13:03:25,865 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Advance` DROP INDEX `modified`
2025-06-12 13:03:25,899 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Withholding Account` DROP INDEX `modified`
2025-06-12 13:03:25,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Advance` DROP INDEX `modified`
2025-06-12 13:03:25,966 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Payment Reconciliation Log Allocations` DROP INDEX `modified`
2025-06-12 13:03:26,001 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Taxes and Charges` DROP INDEX `modified`
2025-06-12 13:03:26,034 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Closing Entry Taxes` DROP INDEX `modified`
2025-06-12 13:03:26,067 WARNING database DDL Query made to DB:
ALTER TABLE `tabEvent Participants` DROP INDEX `modified`
2025-06-12 13:03:26,100 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form Field` DROP INDEX `modified`
2025-06-12 13:03:26,133 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` DROP INDEX `modified`
2025-06-12 13:03:26,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` DROP INDEX `modified`
2025-06-12 13:03:26,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Taxes and Charges` DROP INDEX `modified`
2025-06-12 13:03:26,236 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Operation` DROP INDEX `modified`
2025-06-12 13:03:26,268 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Document State` DROP INDEX `modified`
2025-06-12 13:03:26,300 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Shortcut` DROP INDEX `modified`
2025-06-12 13:03:26,333 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` DROP INDEX `modified`
2025-06-12 13:03:26,365 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Transition` DROP INDEX `modified`
2025-06-12 13:03:26,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Default` DROP INDEX `modified`
